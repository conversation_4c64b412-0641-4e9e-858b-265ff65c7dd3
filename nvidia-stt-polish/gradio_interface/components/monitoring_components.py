#!/usr/bin/env python3
"""
📊 Monitoring Components for Gradio Interface
Real-time system monitoring, health checks, and performance analytics
"""

import gradio as gr
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random

class MonitoringComponents:
    """📊 System monitoring components"""
    
    def __init__(self):
        self.metrics_history = []
        self.alerts_history = []
        self.service_status = {
            "nvidia_stt": "healthy",
            "python_mixer": "healthy", 
            "gemma_ai": "degraded",
            "database": "healthy",
            "file_storage": "healthy"
        }
    
    def create_monitoring_interface(self):
        """📊 Create monitoring interface"""
        
        gr.HTML('<div class="cosmic-card"><h2>📊 System Monitoring & Analytics</h2></div>')
        
        with gr.Tabs():
            
            # Real-time Dashboard
            with gr.Tab("📈 Real-time Dashboard"):
                self.create_realtime_dashboard()
            
            # System Health
            with gr.Tab("🏥 System Health"):
                self.create_health_dashboard()
            
            # Performance Analytics
            with gr.Tab("📊 Performance Analytics"):
                self.create_analytics_dashboard()
            
            # Alerts & Notifications
            with gr.Tab("🚨 Alerts & Notifications"):
                self.create_alerts_dashboard()
    
    def create_realtime_dashboard(self):
        """📈 Create real-time monitoring dashboard"""
        
        gr.HTML('<h3>📈 Real-time System Metrics</h3>')
        
        # Key metrics row
        with gr.Row():
            cpu_metric = gr.HTML(
                value='<div class="metric-display"><h4>💻 CPU Usage</h4><h2>45.2%</h2></div>'
            )
            memory_metric = gr.HTML(
                value='<div class="metric-display"><h4>🧠 Memory</h4><h2>67.8%</h2></div>'
            )
            gpu_metric = gr.HTML(
                value='<div class="metric-display"><h4>🎮 GPU Usage</h4><h2>23.1%</h2></div>'
            )
            network_metric = gr.HTML(
                value='<div class="metric-display"><h4>🌐 Network</h4><h2>12.4 MB/s</h2></div>'
            )
        
        # Performance charts
        with gr.Row():
            with gr.Column():
                performance_chart = gr.Plot(
                    label="📊 System Performance (Last Hour)",
                    value=self.create_performance_chart()
                )
            
            with gr.Column():
                resource_chart = gr.Plot(
                    label="🖥️ Resource Utilization",
                    value=self.create_resource_chart()
                )
        
        # Service status indicators
        gr.HTML('<h3>🔗 Service Status</h3>')
        
        with gr.Row():
            nvidia_status = gr.HTML(
                value='<span class="status-healthy">🟢 NVIDIA STT: Online</span>'
            )
            mixer_status = gr.HTML(
                value='<span class="status-healthy">🟢 Python_Mixer: Online</span>'
            )
            gemma_status = gr.HTML(
                value='<span class="status-warning">🟡 Gemma AI: Degraded</span>'
            )
            db_status = gr.HTML(
                value='<span class="status-healthy">🟢 Database: Online</span>'
            )
        
        # Auto-refresh controls
        with gr.Row():
            auto_refresh = gr.Checkbox(
                value=True,
                label="🔄 Auto Refresh",
                interactive=True
            )
            
            refresh_interval = gr.Slider(
                minimum=5,
                maximum=60,
                value=10,
                step=5,
                label="⏱️ Refresh Interval (seconds)",
                interactive=True
            )
            
            manual_refresh_btn = gr.Button("🔄 Refresh Now", variant="primary")
        
        # Event handlers
        manual_refresh_btn.click(
            fn=self.refresh_metrics,
            outputs=[cpu_metric, memory_metric, gpu_metric, network_metric, performance_chart]
        )
    
    def create_health_dashboard(self):
        """🏥 Create system health dashboard"""
        
        gr.HTML('<h3>🏥 System Health Overview</h3>')
        
        with gr.Row():
            with gr.Column(scale=2):
                # Health score
                health_score = gr.Plot(
                    label="🎯 Overall Health Score",
                    value=self.create_health_gauge(87.5)
                )
                
                # Service health details
                service_health_table = gr.Dataframe(
                    headers=["Service", "Status", "Response Time", "Last Check", "Uptime"],
                    datatype=["str", "str", "str", "str", "str"],
                    value=self.get_service_health_data(),
                    label="🔗 Service Health Details"
                )
            
            with gr.Column(scale=1):
                # Health indicators
                gr.HTML('<h4>🎯 Health Indicators</h4>')
                
                availability_indicator = gr.HTML(
                    value='<div class="metric-display"><h4>📈 Availability</h4><h2>99.9%</h2></div>'
                )
                
                response_time_indicator = gr.HTML(
                    value='<div class="metric-display"><h4>⚡ Avg Response</h4><h2>245ms</h2></div>'
                )
                
                error_rate_indicator = gr.HTML(
                    value='<div class="metric-display"><h4>❌ Error Rate</h4><h2>0.1%</h2></div>'
                )
                
                # Quick actions
                gr.HTML('<h4>⚡ Quick Actions</h4>')
                
                health_check_btn = gr.Button("🏥 Run Health Check", variant="primary")
                restart_services_btn = gr.Button("🔄 Restart Services", variant="secondary")
                clear_cache_btn = gr.Button("🗑️ Clear Cache", variant="secondary")
        
        # Health trends
        gr.HTML('<h3>📈 Health Trends</h3>')
        
        health_trends_chart = gr.Plot(
            label="📊 Health Trends (Last 24 Hours)",
            value=self.create_health_trends_chart()
        )
        
        # Event handlers
        health_check_btn.click(
            fn=self.run_health_check,
            outputs=[service_health_table, availability_indicator, response_time_indicator]
        )
    
    def create_analytics_dashboard(self):
        """📊 Create performance analytics dashboard"""
        
        gr.HTML('<h3>📊 Performance Analytics</h3>')
        
        # Time range selector
        with gr.Row():
            time_range = gr.Dropdown(
                choices=["Last Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days"],
                value="Last 24 Hours",
                label="📅 Time Range",
                scale=1
            )
            
            metric_selector = gr.CheckboxGroup(
                choices=["CPU", "Memory", "GPU", "Network", "Disk", "Response Time"],
                value=["CPU", "Memory", "GPU"],
                label="📊 Metrics to Display",
                scale=2
            )
        
        # Analytics charts
        with gr.Row():
            with gr.Column():
                performance_analytics = gr.Plot(
                    label="📈 Performance Analytics",
                    value=self.create_analytics_chart()
                )
            
            with gr.Column():
                distribution_chart = gr.Plot(
                    label="📊 Metric Distribution",
                    value=self.create_distribution_chart()
                )
        
        # Statistics table
        gr.HTML('<h3>📋 Performance Statistics</h3>')
        
        stats_table = gr.Dataframe(
            headers=["Metric", "Current", "Average", "Min", "Max", "Trend"],
            datatype=["str", "str", "str", "str", "str", "str"],
            value=self.get_performance_stats(),
            label="📊 Performance Statistics"
        )
        
        # Correlation analysis
        correlation_chart = gr.Plot(
            label="🔗 Metric Correlations",
            value=self.create_correlation_chart()
        )
        
        # Event handlers
        time_range.change(
            fn=self.update_analytics,
            inputs=[time_range, metric_selector],
            outputs=[performance_analytics, stats_table]
        )
    
    def create_alerts_dashboard(self):
        """🚨 Create alerts and notifications dashboard"""
        
        gr.HTML('<h3>🚨 Alerts & Notifications</h3>')
        
        with gr.Row():
            with gr.Column(scale=1):
                # Alert configuration
                gr.HTML('<h4>⚙️ Alert Configuration</h4>')
                
                cpu_threshold = gr.Slider(
                    minimum=50,
                    maximum=100,
                    value=80,
                    label="💻 CPU Alert Threshold (%)",
                    interactive=True
                )
                
                memory_threshold = gr.Slider(
                    minimum=50,
                    maximum=100,
                    value=85,
                    label="🧠 Memory Alert Threshold (%)",
                    interactive=True
                )
                
                response_threshold = gr.Slider(
                    minimum=100,
                    maximum=5000,
                    value=1000,
                    label="⚡ Response Time Threshold (ms)",
                    interactive=True
                )
                
                error_threshold = gr.Slider(
                    minimum=0.1,
                    maximum=10.0,
                    value=1.0,
                    label="❌ Error Rate Threshold (%)",
                    interactive=True
                )
                
                save_thresholds_btn = gr.Button("💾 Save Thresholds", variant="primary")
            
            with gr.Column(scale=2):
                # Active alerts
                gr.HTML('<h4>🚨 Active Alerts</h4>')
                
                active_alerts = gr.HTML(
                    value=self.format_active_alerts()
                )
                
                # Alert history
                gr.HTML('<h4>📜 Alert History</h4>')
                
                alert_history_table = gr.Dataframe(
                    headers=["Timestamp", "Type", "Severity", "Message", "Status"],
                    datatype=["str", "str", "str", "str", "str"],
                    value=self.get_alert_history(),
                    label="Alert History"
                )
        
        # Alert statistics
        gr.HTML('<h3>📊 Alert Statistics</h3>')
        
        with gr.Row():
            alert_stats_chart = gr.Plot(
                label="📊 Alerts by Type",
                value=self.create_alert_stats_chart()
            )
            
            alert_timeline = gr.Plot(
                label="📈 Alert Timeline",
                value=self.create_alert_timeline_chart()
            )
        
        # Event handlers
        save_thresholds_btn.click(
            fn=self.save_alert_thresholds,
            inputs=[cpu_threshold, memory_threshold, response_threshold, error_threshold],
            outputs=[active_alerts]
        )
    
    def create_sample_chart(self):
        """📊 Create sample performance chart"""
        
        # Generate sample data
        time_points = pd.date_range(start=datetime.now() - timedelta(hours=1), 
                                   end=datetime.now(), freq='1min')
        
        cpu_data = np.random.normal(45, 10, len(time_points))
        memory_data = np.random.normal(65, 8, len(time_points))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=time_points,
            y=cpu_data,
            mode='lines',
            name='CPU Usage (%)',
            line=dict(color='#ff6b6b')
        ))
        
        fig.add_trace(go.Scatter(
            x=time_points,
            y=memory_data,
            mode='lines',
            name='Memory Usage (%)',
            line=dict(color='#4ecdc4')
        ))
        
        fig.update_layout(
            title="📊 System Performance",
            xaxis_title="Time",
            yaxis_title="Usage (%)",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def create_performance_chart(self):
        """📈 Create performance monitoring chart"""
        return self.create_sample_chart()
    
    def create_resource_chart(self):
        """🖥️ Create resource utilization chart"""
        
        resources = ['CPU', 'Memory', 'GPU', 'Disk', 'Network']
        values = [45.2, 67.8, 23.1, 34.5, 12.4]
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        
        fig = go.Figure(data=go.Bar(
            x=resources,
            y=values,
            marker_color=colors,
            text=[f"{v}%" for v in values],
            textposition='auto'
        ))
        
        fig.update_layout(
            title="🖥️ Resource Utilization",
            yaxis_title="Usage (%)",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def create_health_gauge(self, health_score):
        """🎯 Create health score gauge"""
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=health_score,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "System Health Score"},
            delta={'reference': 90},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "#00d4ff"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "yellow"},
                    {'range': [80, 95], 'color': "orange"},
                    {'range': [95, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def get_service_health_data(self):
        """🔗 Get service health data"""
        
        return [
            ["NVIDIA STT", "🟢 Healthy", "245ms", "2 min ago", "99.9%"],
            ["Python_Mixer", "🟢 Healthy", "180ms", "1 min ago", "99.8%"],
            ["Gemma AI", "🟡 Degraded", "1.2s", "5 min ago", "95.2%"],
            ["Database", "🟢 Healthy", "15ms", "30s ago", "99.9%"],
            ["File Storage", "🟢 Healthy", "95ms", "1 min ago", "99.7%"]
        ]
    
    def create_health_trends_chart(self):
        """📈 Create health trends chart"""
        
        time_points = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                                   end=datetime.now(), freq='1H')
        
        health_scores = np.random.normal(87, 5, len(time_points))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=time_points,
            y=health_scores,
            mode='lines+markers',
            name='Health Score',
            line=dict(color='#00d4ff', width=3),
            fill='tonexty',
            fillcolor='rgba(0, 212, 255, 0.3)'
        ))
        
        fig.update_layout(
            title="📈 System Health Trends",
            xaxis_title="Time",
            yaxis_title="Health Score",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def create_analytics_chart(self):
        """📊 Create analytics chart"""
        return self.create_sample_chart()
    
    def create_distribution_chart(self):
        """📊 Create metric distribution chart"""
        
        data = np.random.normal(50, 15, 1000)
        
        fig = go.Figure(data=go.Histogram(
            x=data,
            nbinsx=30,
            marker_color='#4ecdc4',
            opacity=0.7
        ))
        
        fig.update_layout(
            title="📊 CPU Usage Distribution",
            xaxis_title="CPU Usage (%)",
            yaxis_title="Frequency",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def get_performance_stats(self):
        """📋 Get performance statistics"""
        
        return [
            ["CPU Usage", "45.2%", "42.8%", "12.1%", "89.3%", "📈 Stable"],
            ["Memory Usage", "67.8%", "65.2%", "45.6%", "92.1%", "📈 Increasing"],
            ["GPU Usage", "23.1%", "28.5%", "0.0%", "95.7%", "📉 Decreasing"],
            ["Response Time", "245ms", "280ms", "120ms", "1.2s", "📈 Improving"],
            ["Error Rate", "0.1%", "0.2%", "0.0%", "1.5%", "📈 Stable"]
        ]
    
    def create_correlation_chart(self):
        """🔗 Create correlation chart"""
        
        metrics = ['CPU', 'Memory', 'GPU', 'Network', 'Response Time']
        correlation_matrix = np.random.rand(5, 5)
        
        fig = go.Figure(data=go.Heatmap(
            z=correlation_matrix,
            x=metrics,
            y=metrics,
            colorscale='RdYlBu',
            text=correlation_matrix,
            texttemplate="%{text:.2f}",
            textfont={"size": 10}
        ))
        
        fig.update_layout(
            title="🔗 Metric Correlations",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def format_active_alerts(self):
        """🚨 Format active alerts"""
        
        return """
        <div class="cosmic-card">
            <h4>🚨 Active Alerts (2)</h4>
            <p><span class="status-warning">⚠️ High Memory Usage</span> - Memory usage at 89%</p>
            <p><span class="status-warning">⚠️ Slow Response</span> - Gemma AI response time > 1s</p>
        </div>
        """
    
    def get_alert_history(self):
        """📜 Get alert history"""
        
        return [
            ["2024-01-15 14:30", "Performance", "Warning", "High CPU usage detected", "Resolved"],
            ["2024-01-15 13:45", "Service", "Critical", "Gemma AI service degraded", "Active"],
            ["2024-01-15 12:20", "Network", "Warning", "High network latency", "Resolved"],
            ["2024-01-15 11:15", "Memory", "Warning", "Memory usage above threshold", "Active"],
            ["2024-01-15 10:30", "Disk", "Info", "Disk cleanup completed", "Resolved"]
        ]
    
    def create_alert_stats_chart(self):
        """📊 Create alert statistics chart"""
        
        alert_types = ['Performance', 'Service', 'Network', 'Memory', 'Disk']
        alert_counts = [15, 8, 12, 6, 4]
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        
        fig = go.Figure(data=go.Pie(
            labels=alert_types,
            values=alert_counts,
            marker_colors=colors,
            hole=0.4
        ))
        
        fig.update_layout(
            title="📊 Alerts by Type (Last 7 Days)",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def create_alert_timeline_chart(self):
        """📈 Create alert timeline chart"""
        
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), 
                             end=datetime.now(), freq='1D')
        
        alert_counts = np.random.poisson(3, len(dates))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=alert_counts,
            mode='lines+markers',
            name='Daily Alerts',
            line=dict(color='#ff6b6b', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title="📈 Alert Timeline (Last 7 Days)",
            xaxis_title="Date",
            yaxis_title="Number of Alerts",
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def refresh_metrics(self):
        """🔄 Refresh system metrics"""
        
        # Generate new random metrics
        cpu = random.uniform(20, 80)
        memory = random.uniform(40, 90)
        gpu = random.uniform(10, 60)
        network = random.uniform(5, 25)
        
        return (
            f'<div class="metric-display"><h4>💻 CPU Usage</h4><h2>{cpu:.1f}%</h2></div>',
            f'<div class="metric-display"><h4>🧠 Memory</h4><h2>{memory:.1f}%</h2></div>',
            f'<div class="metric-display"><h4>🎮 GPU Usage</h4><h2>{gpu:.1f}%</h2></div>',
            f'<div class="metric-display"><h4>🌐 Network</h4><h2>{network:.1f} MB/s</h2></div>',
            self.create_performance_chart()
        )
    
    def run_health_check(self):
        """🏥 Run comprehensive health check"""
        
        # Simulate health check
        updated_data = [
            ["NVIDIA STT", "🟢 Healthy", "230ms", "Just now", "99.9%"],
            ["Python_Mixer", "🟢 Healthy", "165ms", "Just now", "99.8%"],
            ["Gemma AI", "🟢 Healthy", "890ms", "Just now", "98.5%"],
            ["Database", "🟢 Healthy", "12ms", "Just now", "99.9%"],
            ["File Storage", "🟢 Healthy", "88ms", "Just now", "99.7%"]
        ]
        
        return (
            updated_data,
            '<div class="metric-display"><h4>📈 Availability</h4><h2>99.9%</h2></div>',
            '<div class="metric-display"><h4>⚡ Avg Response</h4><h2>215ms</h2></div>'
        )
    
    def update_analytics(self, time_range, metrics):
        """📊 Update analytics based on selection"""
        return self.create_analytics_chart(), self.get_performance_stats()
    
    def save_alert_thresholds(self, cpu, memory, response, error):
        """💾 Save alert thresholds"""
        
        return f"""
        <div class="cosmic-card">
            <h4>✅ Alert Thresholds Updated</h4>
            <p>CPU: {cpu}% | Memory: {memory}% | Response: {response}ms | Error: {error}%</p>
        </div>
        """
