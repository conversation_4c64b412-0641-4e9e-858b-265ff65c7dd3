from matplotlib import cbook, colorbar, colors, artist

from typing import overload
import numpy as np
from numpy.typing import ArrayLike


class Colorizer:
    colorbar: colorbar.Colorbar | None
    callbacks: cbook.CallbackRegistry
    def __init__(
        self,
        cmap: str | colors.Colormap | None = ...,
        norm: str | colors.Normalize | None = ...,
    ) -> None: ...
    @property
    def norm(self) -> colors.Normalize: ...
    @norm.setter
    def norm(self, norm: colors.Normalize | str | None) -> None: ...
    def to_rgba(
        self,
        x: np.ndarray,
        alpha: float | ArrayLike | None = ...,
        bytes: bool = ...,
        norm: bool = ...,
    ) -> np.ndarray: ...
    def autoscale(self, A: ArrayLike) -> None: ...
    def autoscale_None(self, A: ArrayLike) -> None: ...
    @property
    def cmap(self) -> colors.Colormap: ...
    @cmap.setter
    def cmap(self, cmap: colors.Colormap | str | None) -> None: ...
    def get_clim(self) -> tuple[float, float]: ...
    def set_clim(self, vmin: float | tuple[float, float] | None = ..., vmax: float | None = ...) -> None: ...
    def changed(self) -> None: ...
    @property
    def vmin(self) -> float | None: ...
    @vmin.setter
    def vmin(self, value: float | None) -> None: ...
    @property
    def vmax(self) -> float | None: ...
    @vmax.setter
    def vmax(self, value: float | None) -> None: ...
    @property
    def clip(self) -> bool: ...
    @clip.setter
    def clip(self, value: bool) -> None: ...


class _ColorizerInterface:
    cmap: colors.Colormap
    colorbar: colorbar.Colorbar | None
    callbacks: cbook.CallbackRegistry
    def to_rgba(
        self,
        x: np.ndarray,
        alpha: float | ArrayLike | None = ...,
        bytes: bool = ...,
        norm: bool = ...,
    ) -> np.ndarray: ...
    def get_clim(self) -> tuple[float, float]: ...
    def set_clim(self, vmin: float | tuple[float, float] | None = ..., vmax: float | None = ...) -> None: ...
    def get_alpha(self) -> float | None: ...
    def get_cmap(self) -> colors.Colormap: ...
    def set_cmap(self, cmap: str | colors.Colormap) -> None: ...
    @property
    def norm(self) -> colors.Normalize: ...
    @norm.setter
    def norm(self, norm: colors.Normalize | str | None) -> None: ...
    def set_norm(self, norm: colors.Normalize | str | None) -> None: ...
    def autoscale(self) -> None: ...
    def autoscale_None(self) -> None: ...


class _ScalarMappable(_ColorizerInterface):
    def __init__(
        self,
        norm: colors.Normalize | None = ...,
        cmap: str | colors.Colormap | None = ...,
        *,
        colorizer: Colorizer | None = ...,
        **kwargs
    ) -> None: ...
    def set_array(self, A: ArrayLike | None) -> None: ...
    def get_array(self) -> np.ndarray | None: ...
    def changed(self) -> None: ...


class ColorizingArtist(_ScalarMappable, artist.Artist):
    callbacks: cbook.CallbackRegistry
    def __init__(
        self,
        colorizer: Colorizer,
        **kwargs
    ) -> None: ...
    def set_array(self, A: ArrayLike | None) -> None: ...
    def get_array(self) -> np.ndarray | None: ...
    def changed(self) -> None: ...
    @property
    def colorizer(self) -> Colorizer: ...
    @colorizer.setter
    def colorizer(self, cl: Colorizer) -> None: ...
