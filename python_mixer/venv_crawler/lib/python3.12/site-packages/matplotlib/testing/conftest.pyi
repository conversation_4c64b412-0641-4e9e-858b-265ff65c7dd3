from types import ModuleType

import pytest

def pytest_configure(config: pytest.Config) -> None: ...
def pytest_unconfigure(config: pytest.Config) -> None: ...
@pytest.fixture
def mpl_test_settings(request: pytest.FixtureRequest) -> None: ...
@pytest.fixture
def pd() -> ModuleType: ...
@pytest.fixture
def xr() -> ModuleType: ...
@pytest.fixture
def text_placeholders(monkeypatch: pytest.MonkeyPatch) -> None: ...
